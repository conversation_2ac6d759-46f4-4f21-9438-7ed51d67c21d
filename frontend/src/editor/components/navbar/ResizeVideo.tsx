import React, { use<PERSON><PERSON>back, useEffect, useState } from "react";
import { <PERSON>, IconButton, Tooltip, Typography } from "@mui/material";
import AspectRatioIcon from "@mui/icons-material/AspectRatio";
import LandscapeIcon from "@mui/icons-material/Landscape";
import {
  Instagram as InstagramIcon,
  MusicVideo as TikTokIcon,
  YouTube as YouTubeIcon,
  OndemandVideo as YouTubeShortIcon,
} from "@mui/icons-material";
import { StoreContext } from "../../../store";
import { CustomPopover } from "..";
import { useLanguage } from "../../../i18n/LanguageContext";
import { observer } from "mobx-react-lite";

// Common styles
const COMMON_STYLES = {
  flexColumnWithGap: {
    display: "flex",
    flexDirection: "column",
    gap: 0,
  },
};

export interface ResizeValue {
  width: number;
  height: number;
  name: string;
}

export const RESIZE_OPTIONS = [
  {
    label: "original",
    icon: LandscapeIcon,
    value: {
      width: 1280,
      height: 720,
      name: "Original aspect ratio",
    },
    platforms: "",
    isOriginal: true,
    aspectRatio: 16 / 9,
  },
  {
    label: "aspect_ratio_16_9",
    icon: AspectRatioIcon,
    value: {
      width: 1280,
      height: 720,
      name: "16:9",
    },
    platforms: "YouTube ads",
    aspectRatio: 16 / 9,
  },
  {
    label: "aspect_ratio_4_3",
    icon: AspectRatioIcon,
    value: {
      width: 960,
      height: 720,
      name: "4:3",
    },
    platforms: "LinkedIn ads, Facebook ads",
    aspectRatio: 4 / 3,
  },
  {
    label: "aspect_ratio_2_1",
    icon: AspectRatioIcon,
    value: {
      width: 1280,
      height: 640,
      name: "2:1",
    },
    platforms: "",
    aspectRatio: 2 / 1,
  },
  {
    label: "aspect_ratio_9_16",
    icon: TikTokIcon,
    value: {
      width: 720,
      height: 1280,
      name: "9:16",
    },
    platforms: "TikTok, TikTok ads",
    aspectRatio: 9 / 16,
  },
  {
    label: "aspect_ratio_1_1",
    icon: InstagramIcon,
    value: {
      width: 720,
      height: 720,
      name: "1:1",
    },
    platforms: "Instagram posts",
    aspectRatio: 1 / 1,
  },
  {
    label: "aspect_ratio_3_4",
    icon: AspectRatioIcon,
    value: {
      width: 720,
      height: 960,
      name: "3:4",
    },
    platforms: "",
    aspectRatio: 3 / 4,
  },
];

interface AspectRatioBoxProps {
  aspectRatio: number;
  isSelected: boolean;
}

const AspectRatioBox = React.memo(
  ({ aspectRatio, isSelected }: AspectRatioBoxProps) => {
    // 基础尺寸，然后根据比例调整
    const baseSize = 24;
    let width, height;

    if (aspectRatio >= 1) {
      // 横向或正方形
      width = baseSize;
      height = baseSize / aspectRatio;
    } else {
      // 竖向
      width = baseSize * aspectRatio;
      height = baseSize;
    }

    return (
      <Box
        sx={{
          width: width,
          height: height,
          border: isSelected
            ? "2px solid #1976d2"
            : "2px solid rgba(0, 0, 0, 0.23)",
          borderRadius: "2px",
          bgcolor: isSelected ? "primary.light" : "rgba(25, 118, 210, 0.1)",
          transition: "all 0.2s ease-in-out",
          minWidth: 12,
          minHeight: 12,
        }}
      />
    );
  }
);

AspectRatioBox.displayName = "AspectRatioBox";

interface ResizeOptionProps {
  label: string;
  Icon: React.ElementType;
  value: ResizeValue;
  platforms?: string;
  handleResize: (payload: ResizeValue) => void;
  t: (key: string) => string;
  isSelected: boolean;
  isOriginal?: boolean;
  aspectRatio: number;
}

const ResizeOption = React.memo(
  ({
    label,
    Icon,
    value,
    platforms,
    handleResize,
    t,
    isSelected,
    isOriginal,
    aspectRatio,
  }: ResizeOptionProps) => {
    const handleClick = useCallback(() => {
      handleResize(value);
    }, [handleResize, value]);

    return (
      <Box
        onClick={handleClick}
        sx={{
          display: "flex",
          alignItems: "center",
          gap: 1.5,
          cursor: "pointer",
          p: 2,
          minHeight: 56,
          borderRadius: 0,
          transition: "all 0.2s ease-in-out",
          "&:hover": {
            bgcolor: "rgba(0, 0, 0, 0.04)",
          },
          borderBottom: "1px solid rgba(0, 0, 0, 0.06)",
          "&:last-child": {
            borderBottom: "none",
          },
        }}
      >
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            minWidth: 32,
            height: 32,
          }}
        >
          <AspectRatioBox aspectRatio={aspectRatio} isSelected={isSelected} />
        </Box>
        <Box sx={{ flex: 1 }}>
          <Typography
            variant="body2"
            fontWeight={isOriginal ? "500" : "400"}
            sx={{
              color: "text.primary",
              fontSize: "14px",
              lineHeight: 1.2,
            }}
          >
            {value.name}
          </Typography>
          {platforms && (
            <Typography
              variant="caption"
              sx={{
                display: "block",
                mt: 0.5,
                color: "text.secondary",
                fontSize: "12px",
                lineHeight: 1.2,
              }}
            >
              {platforms}
            </Typography>
          )}
        </Box>
        {isSelected && (
          <Box
            sx={{
              width: 16,
              height: 16,
              borderRadius: "50%",
              bgcolor: "#1976d2",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
            }}
          >
            <Box
              sx={{
                width: 6,
                height: 6,
                borderRadius: "50%",
                bgcolor: "white",
              }}
            />
          </Box>
        )}
      </Box>
    );
  }
);

ResizeOption.displayName = "ResizeOption";

export const ResizeVideo = observer(() => {
  const store = React.useContext(StoreContext);
  const { t } = useLanguage();
  const [selectedRatio, setSelectedRatio] = useState(
    RESIZE_OPTIONS[0].value.name
  );

  const handleResize = useCallback(
    (payload: ResizeValue) => {
      store.setCanvasSize(payload.width, payload.height);
      store.saveChange("adjust_canvas");
      setSelectedRatio(payload.name);
    },
    [store]
  );

  // 根据当前画布尺寸自动匹配选中项
  useEffect(() => {
    const { canvasWidth, canvasHeight } = store;
    if (!canvasWidth || !canvasHeight) return;

    // 先尝试宽高精确匹配
    const exact = RESIZE_OPTIONS.find(
      (opt) =>
        opt.value.width === canvasWidth && opt.value.height === canvasHeight
    );
    if (exact) {
      setSelectedRatio(exact.value.name);
      return;
    }

    // 否则按比例近似匹配（容差）
    const currentRatio = canvasWidth / canvasHeight;
    const TOLERANCE = 0.01;
    const approx = RESIZE_OPTIONS.find(
      (opt) => Math.abs(opt.aspectRatio - currentRatio) < TOLERANCE
    );
    if (approx) {
      setSelectedRatio(approx.value.name);
      return;
    }
  }, [store.canvasWidth, store.canvasHeight]);

  return (
    <CustomPopover
      customTrigger={
        <Tooltip title={t("ratio")} arrow>
          <IconButton>
            <AspectRatioIcon sx={{ fontSize: 22 }} />
          </IconButton>
        </Tooltip>
      }
      minWidth={320}
      popoverProps={{
        sx: {
          "& .MuiPaper-root": {
            p: 0,
            boxShadow: "0px 4px 20px rgba(0, 0, 0, 0.1)",
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "8px",
            overflow: "hidden",
          },
        },
      }}
    >
      <Box sx={{ ...COMMON_STYLES.flexColumnWithGap }}>
        {RESIZE_OPTIONS.map((option, index) => (
          <ResizeOption
            key={index}
            label={option.label}
            Icon={option.icon}
            value={option.value}
            platforms={option.platforms}
            handleResize={handleResize}
            t={t}
            isSelected={selectedRatio === option.value.name}
            isOriginal={option.isOriginal}
            aspectRatio={option.aspectRatio}
          />
        ))}
      </Box>
    </CustomPopover>
  );
});

ResizeVideo.displayName = "ResizeVideo";
