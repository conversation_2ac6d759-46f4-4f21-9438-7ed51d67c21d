import React, { useCallback, useState, useEffect } from "react";
import { Box, IconButton, Tooltip, Typography, Divider } from "@mui/material";
import LandscapeIcon from "@mui/icons-material/Landscape";
import InfoOutlinedIcon from "@mui/icons-material/InfoOutlined";
import FormatColorResetIcon from "@mui/icons-material/FormatColorReset";
import CheckIcon from "@mui/icons-material/Check";
import { StoreContext } from "../../../store";
import { CustomPopover } from "..";
import { useLanguage } from "../../../i18n/LanguageContext";

export const BACKGROUND_COLORS = [
  "#FFFFFF", // White
  "#000000", // Black
  "#F5F5F5", // Light Gray
  "#333333", // Dark Gray
  "#FF5252", // Red
  "#4CAF50", // Green
  "#2196F3", // Blue
  "#FFC107", // Yellow
  "#9C27B0", // Purple
  "#FF9800", // Orange
];

// Brand colors
export const BRAND_COLORS = [
  "#4DD0E1", // Teal/Cyan
  "#7B1FA2", // Purple
];

// Recommended colors - 25 professional colors optimized for commercial video backgrounds
export const RECOMMENDED_COLORS = [
  // Essential neutrals - perfect for any commercial content
  "#FFFFFF", // Pure White - clean, professional
  "#F8F9FA", // Off White - softer than pure white
  "#E9ECEF", // Light Gray - subtle, elegant
  "#6C757D", // Medium Gray - balanced neutral
  "#212529", // Charcoal - sophisticated dark

  // Professional blues - trusted, corporate
  "#E3F2FD", // Very Light Blue - calming background
  "#2196F3", // Material Blue - modern, trustworthy
  "#1976D2", // Blue 700 - professional standard
  "#0D47A1", // Deep Blue - authoritative
  "#001F3F", // Navy - classic corporate

  // Warm and approachable
  "#FFF8E1", // Cream - warm, inviting
  "#FFECB3", // Light Amber - friendly, optimistic
  "#FF9800", // Orange - energetic but professional
  "#E65100", // Deep Orange - confident, bold
  "#3E2723", // Dark Brown - earthy, reliable

  // Success and growth greens
  "#E8F5E8", // Very Light Green - fresh, positive
  "#4CAF50", // Material Green - success, growth
  "#2E7D32", // Green 800 - stable, trustworthy
  "#1B5E20", // Dark Green - premium, sophisticated
  "#263238", // Blue Gray 800 - modern, professional

  // Subtle accent colors
  "#F3E5F5", // Light Purple - creative, innovative
  "#9C27B0", // Purple - luxury, creativity
  "#FCE4EC", // Light Pink - soft, approachable
  "#E91E63", // Pink - dynamic, modern
  "#1A1A1A", // Almost Black - sleek, premium
];

const MAX_RECENT_COLORS = 8;

export const BackgroundSelector = React.memo(() => {
  const store = React.useContext(StoreContext);
  const { t } = useLanguage();
  const [recentColors, setRecentColors] = useState<string[]>([]);
  const [selectedColor, setSelectedColor] = useState<string>("");

  // Load recent colors from localStorage on component mount
  useEffect(() => {
    const storedRecentColors = localStorage.getItem("recentBackgroundColors");
    if (storedRecentColors) {
      setRecentColors(JSON.parse(storedRecentColors));
    }
  }, []);

  // Update selected color when store background color changes
  useEffect(() => {
    setSelectedColor(store.backgroundColor || "transparent");
  }, [store.backgroundColor]);

  const updateRecentColors = useCallback((color: string) => {
    setRecentColors((prevColors) => {
      // Remove the color if it already exists
      const filteredColors = prevColors.filter((c) => c !== color);
      // Add the color to the beginning
      const newColors = [color, ...filteredColors].slice(0, MAX_RECENT_COLORS);
      // Save to localStorage
      localStorage.setItem("recentBackgroundColors", JSON.stringify(newColors));
      return newColors;
    });
  }, []);

  const handleColorSelect = useCallback(
    (color: string) => {
      store.setBackgroundColor(color);
      store.saveChange("adjust_canvas");
      updateRecentColors(color);
    },
    [store, updateRecentColors]
  );

  const renderColorBox = useCallback(
    (color: string, index: number) => {
      const isSelected = selectedColor === color;
      return (
        <Box
          key={index}
          onClick={() => handleColorSelect(color)}
          sx={{
            width: 40,
            height: 40,
            borderRadius: 1,
            cursor: "pointer",
            background: color,
            border: "1px solid #e0e0e0",
            transition: "all 0.2s",
            position: "relative",

            "&:hover": {
              transform: "scale(1.05)",
              boxShadow: isSelected
                ? "0 0 0 1px #2196F3, 0 4px 8px rgba(0,0,0,0.1)"
                : "0 4px 8px rgba(0,0,0,0.1)",
            },
          }}
        >
          {isSelected && (
            <Box
              sx={{
                position: "absolute",
                top: "50%",
                left: "50%",
                transform: "translate(-50%, -50%)",
                width: 20,
                height: 20,
                borderRadius: "50%",
                backgroundColor: "#2196F3",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                boxShadow: "0 2px 6px rgba(0,0,0,0.3)",
              }}
            >
              <CheckIcon sx={{ fontSize: 12, color: "#FFFFFF" }} />
            </Box>
          )}
        </Box>
      );
    },
    [handleColorSelect, selectedColor]
  );

  const popoverContent = (
    <Box sx={{ px: 2, py: 2 }}>
      <Typography variant="body2" sx={{ mb: 2 }}>
        {t("recents")}
      </Typography>
      <Box
        sx={{
          display: "flex",
          flexWrap: "wrap",
          gap: 1,
          mb: 3,
          justifyContent: "flex-start",
        }}
      >
        <Box
          onClick={() => handleColorSelect("transparent")}
          sx={{
            width: 40,
            height: 40,
            borderRadius: 1,
            cursor: "pointer",
            border:
              selectedColor === "transparent"
                ? "2px solid #2196F3"
                : "1px solid #e0e0e0",
            boxShadow:
              selectedColor === "transparent" ? "0 0 0 1px #2196F3" : "none",
            position: "relative",
            transition: "all 0.2s",
            "&:hover": {
              transform: "scale(1.05)",
              boxShadow: "0 4px 8px rgba(0,0,0,0.1)",
            },
            "&::before": {
              content: '""',
              position: "absolute",
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              background: "white",
            },
            "&::after": {
              content: '""',
              position: "absolute",
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              background:
                "linear-gradient(to bottom right, transparent calc(50% - 1px), red, transparent calc(50% + 1px))",
            },
          }}
        >
          {selectedColor === "transparent" && (
            <Box
              sx={{
                position: "absolute",
                top: "50%",
                left: "50%",
                transform: "translate(-50%, -50%)",
                width: 20,
                height: 20,
                borderRadius: "50%",
                backgroundColor: "#2196F3",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                boxShadow: "0 2px 6px rgba(0,0,0,0.3)",
                zIndex: 1,
              }}
            >
              <CheckIcon sx={{ fontSize: 12, color: "#FFFFFF" }} />
            </Box>
          )}
        </Box>
        <Box
          onClick={() => handleColorSelect("")}
          sx={{
            width: 40,
            height: 40,
            borderRadius: 1,
            cursor: "pointer",
            border:
              selectedColor === "" ? "2px solid #2196F3" : "1px solid #e0e0e0",
            boxShadow: selectedColor === "" ? "0 0 0 1px #2196F3" : "none",
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            position: "relative",
            transition: "all 0.2s",
            "&:hover": {
              transform: "scale(1.05)",
              boxShadow: "0 4px 8px rgba(0,0,0,0.1)",
            },
          }}
        >
          <FormatColorResetIcon fontSize="small" />
          {selectedColor === "" && (
            <Box
              sx={{
                position: "absolute",
                top: "50%",
                left: "50%",
                transform: "translate(-50%, -50%)",
                width: 20,
                height: 20,
                borderRadius: "50%",
                backgroundColor: "#2196F3",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                boxShadow: "0 2px 6px rgba(0,0,0,0.3)",
              }}
            >
              <CheckIcon sx={{ fontSize: 12, color: "#FFFFFF" }} />
            </Box>
          )}
        </Box>
        {recentColors.map(renderColorBox)}
      </Box>

      <Typography variant="body2" sx={{ mb: 2 }}>
        {t("recommended")}
      </Typography>
      <Box
        sx={{
          display: "grid",
          gridTemplateColumns: "repeat(5, 1fr)",
          gap: 1,
          justifyItems: "center",
        }}
      >
        {RECOMMENDED_COLORS.map(renderColorBox)}
      </Box>
    </Box>
  );

  return (
    <CustomPopover
      maxWidth={280}
      customTrigger={
        <Tooltip title={t("background")} arrow>
          <IconButton>
            <LandscapeIcon sx={{ fontSize: 22 }} />
          </IconButton>
        </Tooltip>
      }
    >
      {popoverContent}
    </CustomPopover>
  );
});

BackgroundSelector.displayName = "BackgroundSelector";
