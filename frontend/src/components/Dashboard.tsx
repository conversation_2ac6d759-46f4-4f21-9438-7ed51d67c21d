import React, { useState, useEffect, useCallback } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  CardActions,
  CircularProgress,
  Grid,
  Container,
  IconButton,
  Chip,
  useTheme,
  alpha,
  Skeleton,
  Snackbar,
  Dialog,
  DialogTitle,
  DialogContent,
  <PERSON>alogActions,
  TextField,
} from "@mui/material";
import {
  Add as AddIcon,
  PlayArrow as PlayIcon,
  AccessTime as TimeIcon,
  VideoLibrary as VideoIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
} from "@mui/icons-material";
import { observer } from "mobx-react-lite";
import { useNavigate } from "react-router-dom";
import { StoreContext } from "../store";
import { ThemeToggle } from "../theme/ThemeToggle";
import { useLanguage } from "../i18n/LanguageContext";

// 项目元数据接口
interface ProjectMetadata {
  id: string;
  name: string;
  lastModified: Date;
  duration?: number;
  thumbnail?: string;
  elementCount?: number;
  canvasState: string;
}

// 本地存储键
const RECENT_PROJECTS_KEY = "fabric-recent-projects";
const MAX_RECENT_PROJECTS = 10;

const Dashboard: React.FC = observer(() => {
  const store = React.useContext(StoreContext);
  const navigate = useNavigate();
  const theme = useTheme();

  const [recentProjects, setRecentProjects] = useState<ProjectMetadata[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [newProjectDialogOpen, setNewProjectDialogOpen] = useState(false);
  const [newProjectName, setNewProjectName] = useState("");
  const [isCreatingProject, setIsCreatingProject] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 加载最近项目列表
  const loadRecentProjects = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      // 模拟异步加载（实际项目中可能需要从服务器获取）
      await new Promise((resolve) => setTimeout(resolve, 300));

      const saved = localStorage.getItem(RECENT_PROJECTS_KEY);
      if (saved) {
        const projects = JSON.parse(saved) as ProjectMetadata[];
        // 按最后修改时间排序
        projects.sort(
          (a, b) =>
            new Date(b.lastModified).getTime() -
            new Date(a.lastModified).getTime()
        );
        setRecentProjects(projects.slice(0, MAX_RECENT_PROJECTS));
      }
    } catch (error) {
      console.error("Failed to load recent projects:", error);
      setError("加载最近项目失败，请刷新页面重试");
    } finally {
      setIsLoading(false);
    }
  }, []);

  // 保存项目到最近项目列表
  const saveToRecentProjects = useCallback(
    (projectData: Omit<ProjectMetadata, "id">) => {
      try {
        const existing = localStorage.getItem(RECENT_PROJECTS_KEY);
        let projects: ProjectMetadata[] = existing ? JSON.parse(existing) : [];

        const projectId = Date.now().toString();
        const newProject: ProjectMetadata = {
          id: projectId,
          ...projectData,
        };

        // 移除同名项目（如果存在）
        projects = projects.filter((p) => p.name !== projectData.name);

        // 添加新项目到开头
        projects.unshift(newProject);

        // 限制最大数量
        projects = projects.slice(0, MAX_RECENT_PROJECTS);

        localStorage.setItem(RECENT_PROJECTS_KEY, JSON.stringify(projects));
        setRecentProjects(projects);
      } catch (error) {
        console.error("Failed to save to recent projects:", error);
      }
    },
    []
  );

  // 删除项目
  const deleteProject = useCallback(
    (projectId: string) => {
      try {
        const updated = recentProjects.filter((p) => p.id !== projectId);
        localStorage.setItem(RECENT_PROJECTS_KEY, JSON.stringify(updated));
        setRecentProjects(updated);
      } catch (error) {
        console.error("Failed to delete project:", error);
      }
    },
    [recentProjects]
  );

  // 创建新项目
  const createNewProject = useCallback(
    async (projectName?: string) => {
      try {
        setIsCreatingProject(true);
        setError(null);

        // 模拟创建项目的异步操作
        await new Promise((resolve) => setTimeout(resolve, 500));

        // 重置store状态 - 使用destroy然后重新初始化
        store.destroy();

        // 重新初始化基本状态
        store.canvas = null;
        store.editorElements = [];
        store.videos = [];
        store.images = [];
        store.audios = [];
        store.animations = [];
        store.selectedElement = null;

        // 设置项目名称
        const name = projectName || `新项目 ${new Date().toLocaleDateString()}`;
        store.projectManager.setProjectName(name);

        // 保存到最近项目
        const canvasState = store.projectManager.exportCanvasState();
        if (canvasState) {
          saveToRecentProjects({
            name,
            lastModified: new Date(),
            duration: 0,
            elementCount: 0,
            canvasState,
          });
        }

        // 导航到编辑器
        navigate("/editor");
      } catch (error) {
        console.error("创建项目失败:", error);
        setError("创建项目失败，请重试");
      } finally {
        setIsCreatingProject(false);
      }
    },
    [store, navigate, saveToRecentProjects]
  );

  // 打开项目
  const openProject = useCallback(
    (project: ProjectMetadata) => {
      setIsLoading(true);
      try {
        // 首先设置项目名称，这样导入的状态会使用正确的存储键
        store.projectManager.setProjectName(project.name);

        // 导入项目状态，显示加载状态
        const success = store.projectManager.importCanvasState(
          project.canvasState,
          true
        );
        if (success) {
          // 更新最后修改时间
          const updatedProject = { ...project, lastModified: new Date() };
          const updated = recentProjects.map((p) =>
            p.id === project.id ? updatedProject : p
          );
          localStorage.setItem(RECENT_PROJECTS_KEY, JSON.stringify(updated));
          setRecentProjects(updated);

          // 导航到编辑器
          navigate("/editor");
        } else {
          console.error("Failed to load project");
        }
      } catch (error) {
        console.error("Failed to open project:", error);
      } finally {
        setIsLoading(false);
      }
    },
    [store, navigate, recentProjects]
  );

  // 格式化持续时间
  const formatDuration = useCallback((duration?: number) => {
    if (!duration) return "0秒";
    const seconds = Math.floor(duration / 1000);
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;

    if (minutes > 0) {
      return `${minutes}分${remainingSeconds}秒`;
    }
    return `${remainingSeconds}秒`;
  }, []);

  // 处理新项目对话框
  const handleNewProjectDialogOpen = () => {
    setNewProjectName("");
    setNewProjectDialogOpen(true);
    setError(null);
  };

  const handleNewProjectDialogClose = () => {
    if (!isCreatingProject) {
      setNewProjectDialogOpen(false);
      setNewProjectName("");
    }
  };

  const handleCreateProject = async () => {
    if (isCreatingProject) return;

    await createNewProject(newProjectName.trim() || undefined);
    if (!error) {
      setNewProjectDialogOpen(false);
      setNewProjectName("");
    }
  };

  useEffect(() => {
    loadRecentProjects();
  }, [loadRecentProjects]);

  return (
    <Box
      sx={{
        minHeight: "100vh",
        backgroundColor: "background.default",
        backgroundImage:
          theme.palette.mode === "dark"
            ? "linear-gradient(135deg, #0f172a 0%, #1e293b 100%)"
            : "linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)",
      }}
    >
      {/* 顶部导航栏 */}
      <Box
        sx={{
          position: "sticky",
          top: 0,
          zIndex: 100,
          backgroundColor: alpha(theme.palette.background.paper, 0.9),
          backdropFilter: "blur(8px)",
          borderBottom: `1px solid ${theme.palette.divider}`,
        }}
      >
        <Container maxWidth="xl">
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              justifyContent: "space-between",
              py: 2,
            }}
          >
            <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
              <img
                src="/assets/logo.png"
                alt="Fabric Video Editor"
                style={{ height: 40, width: 40 }}
              />
              <Typography variant="h5" fontWeight="bold" color="primary">
                Fabric 视频编辑器
              </Typography>
            </Box>
            <ThemeToggle />
          </Box>
        </Container>
      </Box>
      <Container maxWidth="xl" sx={{ py: 4 }}>
        {/* 欢迎区域 */}
        <Box sx={{ textAlign: "center", mb: 6 }}>
          <Typography variant="h3" fontWeight="bold" gutterBottom>
            欢迎回来
          </Typography>
          <Typography variant="h6" color="text.secondary" sx={{ mb: 4 }}>
            开始创建您的下一个精彩视频项目
          </Typography>

          {/* 创建新项目按钮 */}
          <Button
            variant="contained"
            size="large"
            startIcon={<AddIcon />}
            onClick={handleNewProjectDialogOpen}
            sx={{
              py: 1.5,
              px: 4,
              fontSize: "1.1rem",
              borderRadius: 2,
              boxShadow: theme.shadows[4],
              "&:hover": {
                boxShadow: theme.shadows[8],
              },
            }}
          >
            创建新项目
          </Button>
        </Box>

        {/* 最近项目区域 */}
        <Box>
          <Typography variant="h4" fontWeight="bold" gutterBottom>
            最近项目
          </Typography>

          {isLoading ? (
            <Grid container spacing={{ xs: 2, sm: 3 }}>
              {[...Array(8)].map((_, index) => (
                <Grid
                  key={index}
                  size={{
                    xs: 12,
                    sm: 6,
                    md: 4,
                    xl: 3,
                  }}
                >
                  <Card sx={{ height: "100%" }}>
                    <Skeleton
                      variant="rectangular"
                      height={160}
                      animation="wave"
                    />
                    <CardContent>
                      <Skeleton
                        variant="text"
                        height={28}
                        animation="wave"
                        sx={{ mb: 1 }}
                      />
                      <Skeleton
                        variant="text"
                        height={20}
                        width="70%"
                        animation="wave"
                        sx={{ mb: 1 }}
                      />
                      <Box sx={{ display: "flex", gap: 1, mt: 2 }}>
                        <Skeleton
                          variant="rounded"
                          width={60}
                          height={24}
                          animation="wave"
                        />
                        <Skeleton
                          variant="rounded"
                          width={80}
                          height={24}
                          animation="wave"
                        />
                      </Box>
                    </CardContent>
                  </Card>
                </Grid>
              ))}
            </Grid>
          ) : recentProjects.length === 0 ? (
            <Card
              sx={{
                textAlign: "center",
                py: 10,
                px: 4,
                backgroundColor: alpha(theme.palette.primary.main, 0.05),
                border: `2px dashed ${alpha(theme.palette.primary.main, 0.3)}`,
                borderRadius: 3,
                transition: "all 0.3s ease-in-out",
                "&:hover": {
                  backgroundColor: alpha(theme.palette.primary.main, 0.08),
                  borderColor: alpha(theme.palette.primary.main, 0.5),
                },
              }}
            >
              <Box
                sx={{
                  display: "inline-flex",
                  p: 3,
                  borderRadius: "50%",
                  backgroundColor: alpha(theme.palette.primary.main, 0.1),
                  mb: 3,
                }}
              >
                <VideoIcon sx={{ fontSize: 48, color: "primary.main" }} />
              </Box>
              <Typography variant="h5" fontWeight="bold" gutterBottom>
                开始您的创作之旅
              </Typography>
              <Typography
                variant="body1"
                color="text.secondary"
                sx={{ mb: 4, maxWidth: 400, mx: "auto" }}
              >
                还没有项目？创建您的第一个视频项目，体验强大的在线视频编辑功能
              </Typography>
              <Button
                variant="contained"
                size="large"
                startIcon={<AddIcon />}
                onClick={handleNewProjectDialogOpen}
                sx={{
                  py: 1.5,
                  px: 4,
                  borderRadius: 2,
                  fontSize: "1.1rem",
                  boxShadow: theme.shadows[4],
                  "&:hover": {
                    boxShadow: theme.shadows[8],
                  },
                }}
              >
                创建新项目
              </Button>
            </Card>
          ) : (
            <Grid container spacing={{ xs: 2, sm: 3 }}>
              {recentProjects.map((project) => (
                <Grid
                  key={project.id}
                  size={{
                    xs: 12,
                    sm: 6,
                    md: 4,
                    xl: 3,
                  }}
                >
                  <Card
                    sx={{
                      height: "100%",
                      display: "flex",
                      flexDirection: "column",
                      transition: "all 0.2s ease-in-out",
                      "&:hover": {
                        transform: "translateY(-4px)",
                        boxShadow: theme.shadows[8],
                      },
                    }}
                  >
                    {/* 项目缩略图区域 */}
                    <Box
                      sx={{
                        height: 160,
                        backgroundColor: alpha(theme.palette.primary.main, 0.1),
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                        position: "relative",
                        overflow: "hidden",
                      }}
                    >
                      {project.thumbnail ? (
                        <img
                          src={project.thumbnail}
                          alt={project.name}
                          style={{
                            width: "100%",
                            height: "100%",
                            objectFit: "cover",
                          }}
                        />
                      ) : (
                        <VideoIcon
                          sx={{ fontSize: 48, color: "primary.main" }}
                        />
                      )}

                      {/* 播放按钮覆盖层 */}
                      <Box
                        sx={{
                          position: "absolute",
                          top: 0,
                          left: 0,
                          right: 0,
                          bottom: 0,
                          backgroundColor: "rgba(0, 0, 0, 0.5)",
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "center",
                          opacity: 0,
                          transition: "opacity 0.2s",
                          cursor: "pointer",
                          "&:hover": {
                            opacity: 1,
                          },
                        }}
                        onClick={() => openProject(project)}
                      >
                        <IconButton
                          size="large"
                          sx={{
                            backgroundColor: "rgba(255, 255, 255, 0.9)",
                            "&:hover": {
                              backgroundColor: "white",
                            },
                          }}
                        >
                          <PlayIcon
                            sx={{ fontSize: 32, color: "primary.main" }}
                          />
                        </IconButton>
                      </Box>
                    </Box>

                    <CardContent sx={{ flexGrow: 1 }}>
                      <Typography variant="h6" noWrap gutterBottom>
                        {project.name}
                      </Typography>

                      <Box
                        sx={{
                          display: "flex",
                          alignItems: "center",
                          gap: 1,
                          mb: 1,
                        }}
                      >
                        <TimeIcon
                          sx={{ fontSize: 16, color: "text.secondary" }}
                        />
                        <Typography variant="body2" color="text.secondary">
                          {formatDuration(project.duration)}
                        </Typography>
                      </Box>

                      <Typography
                        variant="body2"
                        color="text.secondary"
                        gutterBottom
                      >
                        最后修改:{" "}
                        {new Date(project.lastModified).toLocaleDateString()}
                      </Typography>

                      {project.elementCount !== undefined && (
                        <Chip
                          label={`${project.elementCount} 个元素`}
                          size="small"
                          variant="outlined"
                        />
                      )}
                    </CardContent>

                    <CardActions
                      sx={{ justifyContent: "space-between", px: 2, pb: 2 }}
                    >
                      <Button
                        size="small"
                        startIcon={<EditIcon />}
                        onClick={() => openProject(project)}
                      >
                        编辑
                      </Button>
                      <IconButton
                        size="small"
                        color="error"
                        onClick={() => deleteProject(project.id)}
                      >
                        <DeleteIcon />
                      </IconButton>
                    </CardActions>
                  </Card>
                </Grid>
              ))}
            </Grid>
          )}
        </Box>
      </Container>
      {/* 新项目对话框 */}
      <Dialog
        open={newProjectDialogOpen}
        onClose={handleNewProjectDialogClose}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>创建新项目</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="项目名称"
            fullWidth
            variant="outlined"
            value={newProjectName}
            onChange={(e) => setNewProjectName(e.target.value)}
            placeholder={`新项目 ${new Date().toLocaleDateString()}`}
            sx={{ mt: 2 }}
          />
        </DialogContent>
        <DialogActions>
          <Button
            onClick={handleNewProjectDialogClose}
            disabled={isCreatingProject}
          >
            取消
          </Button>
          <Button
            onClick={handleCreateProject}
            variant="contained"
            startIcon={
              isCreatingProject ? <CircularProgress size={20} /> : <AddIcon />
            }
            disabled={isCreatingProject}
          >
            {isCreatingProject ? "创建中..." : "创建"}
          </Button>
        </DialogActions>
      </Dialog>
      {/* 错误提示 */}
      <Snackbar
        open={!!error}
        autoHideDuration={6000}
        onClose={() => setError(null)}
        anchorOrigin={{ vertical: "bottom", horizontal: "center" }}
      >
        <Alert
          onClose={() => setError(null)}
          severity="error"
          sx={{ width: "100%" }}
        >
          {error}
        </Alert>
      </Snackbar>
    </Box>
  );
});

export default Dashboard;
