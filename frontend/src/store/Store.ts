import { fabric } from "fabric";
import { makeAutoObservable } from "mobx";
import {
  Animation,
  EditorElement,
  Effect,
  MenuOption,
  TextEditorElement,
  TimeFrame,
  BorderStyle,
  Caption,
  ShapeType,
  Placement,
} from "../types";
import {
  isEditorImageElement,
  isEditorVideoElement,
  isEditorGifElement,
} from "../utils";
import { AnimationManager } from "./AnimationManager";
import { ElementManager } from "./ElementManager";
import { VideoSaver } from "./VideoSaver";
import { CaptionManager } from "./CaptionManager";
import { HistoryManager, HistoryActionType } from "./HistoryManager";
import { TrackManager } from "./TrackManager";
import { TemplateManager } from "./TemplateManager";
import { CanvasManager } from "./CanvasManager";
import { ProjectManager } from "./ProjectManager";
import { TimelineManager } from "./TimelineManager";
import { UploadManager } from "./UploadManager";
import { CONSTANTS } from "./constants";
import { audioManager } from "./AudioManager";

// 使用统一的常量定义

export class Store {
  canvas: fabric.Canvas | null;
  sketch: any; // 添加类型声明
  canvasWidth: number;
  canvasHeight: number;
  backgroundColor: string;
  // projectName: string = "Untitled video";

  // 加载状态相关属性
  isLoading: boolean = false;
  loadingMessage: string = "";

  // 多元素加载状态管理（全局加载提示）
  loadingElements: Set<string> = new Set();
  globalLoadingMessage: string = "";
  // 全局加载进度与阶段（可选）
  globalLoadingProgress: number | null = null; // 0-100，null 表示未知
  globalLoadingStage: string = ""; // 例如："初始化", "下载", "渲染"

  // UI布局相关属性
  timelineHeight: number = 250; // 时间线面板高度，默认250px

  selectedMenuOption: MenuOption;
  audios: string[];
  videos: string[];
  images: string[];
  editorElements: EditorElement[];
  selectedElement: EditorElement | null;
  selectedElements: EditorElement[]; // 多选元素数组

  animations: Animation[];
  captions: Caption[];

  possibleVideoFormats: string[] = [...CONSTANTS.MEDIA.SUPPORTED_VIDEO_FORMATS];
  selectedVideoFormat: string;

  animationFrameId: number | undefined;
  animationManager: AnimationManager | null = null;
  elementManager: ElementManager;
  captionManager: CaptionManager;
  historyManager: HistoryManager;
  trackManager: TrackManager;
  templateManager: TemplateManager;
  canvasManager: CanvasManager;
  projectManager: ProjectManager;
  timelineManager: TimelineManager;
  uploadManager: UploadManager;
  cropObject: fabric.Object | null = null;
  cropRect: fabric.Rect | null = null;

  private debouncedSaveChange: Function;

  // 用于标记是否正在拖拽中的状态
  private _isDraggingTimeFrame: boolean = false;

  // 用于标记是否正在导入状态，防止在导入过程中保存历史记录
  // private _isImporting: boolean = false;

  constructor() {
    this.initializeDefaultValues();
    this.initializeManagers();
    this.setupObservables();
    this.setupDebouncedSave();
    this.initializeTimeline();
    // 延迟初始化历史记录，避免在渲染期间触发状态更新
    // this.initializeHistory(); 将在 StoreProvider 中手动调用
  }

  private initializeDefaultValues() {
    this.canvas = null;
    this.canvasWidth = CONSTANTS.CANVAS.DEFAULT_WIDTH;
    this.canvasHeight = CONSTANTS.CANVAS.DEFAULT_HEIGHT;
    this.backgroundColor = CONSTANTS.CANVAS.DEFAULT_BACKGROUND;

    // 初始化数组
    this.videos = [];
    this.images = [];
    this.audios = [];
    this.editorElements = [];
    this.animations = [];

    // 播放状态
    this.selectedElement = null;
    this.selectedElements = [];

    // UI状态
    this.selectedMenuOption = CONSTANTS.UI.DEFAULT_MENU_OPTION;
    this.selectedVideoFormat = CONSTANTS.MEDIA.DEFAULT_VIDEO_FORMAT;

    // 加载状态
    this.isLoading = CONSTANTS.UI.DEFAULT_LOADING;
    this.loadingMessage = CONSTANTS.UI.DEFAULT_LOADING_MESSAGE;

    // UI布局状态 - 从localStorage加载时间线高度
    const savedTimelineHeight = localStorage.getItem("timeline-height");
    this.timelineHeight = savedTimelineHeight
      ? Math.max(150, Math.min(600, parseInt(savedTimelineHeight, 10)))
      : 250;

    // 内部状态
    this._isDraggingTimeFrame = false;
    // this._isImporting = false;
    this.cropObject = null;
    this.cropRect = null;

    // 视频格式
    this.possibleVideoFormats = [...CONSTANTS.MEDIA.SUPPORTED_VIDEO_FORMATS];
  }

  private setupObservables() {
    makeAutoObservable(this, {
      canvas: false,
      animationManager: false,
      elementManager: false,
      captionManager: false,
      historyManager: false,
      trackManager: false,
      templateManager: false,
      canvasManager: false,
      projectManager: false,
      timelineManager: false,
    });
  }

  private setupDebouncedSave() {
    this.debouncedSaveChange = this.debounce(
      (actionType?: HistoryActionType) => {
        this.projectManager.saveToLocalStorage();
        this.saveToHistory(actionType);
      },
      CONSTANTS.SAVE.DELAY
    );
  }

  private initializeTimeline() {
    // Timeline initialization is now handled by TimelineManager
    // Just check if we need to fit timeline to content
    if (this.editorElements.length > 0) {
      this.fitTimelineToContent();
    }
  }

  private initializeHistory() {
    this.historyManager.initHistory();
  }

  private initializeManagers() {
    this.animationManager = new AnimationManager(this);
    this.elementManager = new ElementManager(this);
    this.captionManager = new CaptionManager(this);
    this.timelineManager = new TimelineManager(this);
    this.historyManager = new HistoryManager(this);
    this.trackManager = new TrackManager(this);
    this.templateManager = new TemplateManager(this);
    this.canvasManager = new CanvasManager(this);
    this.projectManager = new ProjectManager(this);
    this.uploadManager = new UploadManager();
    this.captions = this.captionManager.captions;

    // 初始化轨道
    this.trackManager.initializeTracks();

    // 在开发环境中将store添加到全局，方便调试
    if (process.env.NODE_ENV === "development") {
      (window as any).store = this;
      console.log(
        "🔧 开发模式：store已添加到全局，可使用 window.debugTracks() 和 window.cleanupTracks() 进行调试"
      );
    }
  }

  async setCanvasSize(width: number, height: number) {
    // 保存旧的canvas尺寸用于计算缩放比例
    const oldWidth = this.canvasWidth;
    const oldHeight = this.canvasHeight;

    // 验证16:9比例保护
    const expectedRatio =
      CONSTANTS.CANVAS.DEFAULT_WIDTH / CONSTANTS.CANVAS.DEFAULT_HEIGHT;
    const actualRatio = width / height;
    const tolerance = 0.001;

    if (Math.abs(actualRatio - expectedRatio) > tolerance) {
      console.warn(
        `⚠️ 尝试设置非16:9比例的画布尺寸: ${width}x${height} (比例: ${actualRatio.toFixed(
          3
        )})`
      );
      console.log(`🔧 自动修正为16:9比例...`);

      // 保持宽度，调整高度到正确比例
      height = Math.round(width / expectedRatio);
      console.log(
        `📐 修正后尺寸: ${width}x${height} (比例: ${(width / height).toFixed(
          3
        )})`
      );
    }

    this.canvasWidth = width;
    this.canvasHeight = height;
    if (this.canvas) {
      this.canvas.setWidth(width);
      this.canvas.setHeight(height);
      this.canvas.renderAll();
    }

    // 如果有已存在的元素，进行自适应缩放
    if (this.editorElements.length > 0 && oldWidth > 0 && oldHeight > 0) {
      this.adaptElementsToNewCanvasSize(oldWidth, oldHeight, width, height);
    }

    await this.refreshElements();

    // 确保元素显示顺序按照时间线轨道的先后顺序保持不变
    this.updateCanvasOrderByTrackOrder();

    this.saveChange("adjust_canvas");
  }

  /**
   * 自适应缩放所有元素以适应新的canvas尺寸
   * @param oldWidth 旧canvas宽度
   * @param oldHeight 旧canvas高度
   * @param newWidth 新canvas宽度
   * @param newHeight 新canvas高度
   */
  private adaptElementsToNewCanvasSize(
    oldWidth: number,
    oldHeight: number,
    newWidth: number,
    newHeight: number
  ) {
    console.log(
      `🎯 Canvas自适应缩放开始: ${oldWidth}x${oldHeight} → ${newWidth}x${newHeight}`
    );

    // 计算缩放比例
    const scaleX = newWidth / oldWidth;
    const scaleY = newHeight / oldHeight;

    console.log(`📏 缩放比例: X=${scaleX.toFixed(3)}, Y=${scaleY.toFixed(3)}`);

    // 使用基于主要维度的智能缩放策略
    this.editorElements.forEach((element, index) => {
      if (!element.placement) return;

      const originalPlacement = { ...element.placement };

      const scaledPlacement = this.calculateAdaptiveScaling(
        element.placement,
        scaleX,
        scaleY,
        oldWidth,
        oldHeight,
        newWidth,
        newHeight
      );

      // 更新元素的placement
      element.placement = scaledPlacement;

      // 如果是文本元素，还需要调整fontSize
      if (element.type === "text") {
        const textElement = element as TextEditorElement;
        if (textElement.properties.fontSize) {
          const originalFontSize = textElement.properties.fontSize;
          // 使用主要维度的缩放比例来调整字体大小
          const fontScale = this.calculateFontScale(
            element.placement,
            scaleX,
            scaleY
          );
          textElement.properties.fontSize = Math.round(
            textElement.properties.fontSize * fontScale
          );

          console.log(
            `📝 文本元素 ${index + 1}: 字体 ${originalFontSize}px → ${
              textElement.properties.fontSize
            }px (缩放${fontScale.toFixed(3)})`
          );
        }
      }

      console.log(
        `🔄 元素 ${index + 1} (${element.type}): 位置 (${
          originalPlacement.x
        }, ${originalPlacement.y}) → (${scaledPlacement.x}, ${
          scaledPlacement.y
        }), 尺寸 ${originalPlacement.width}x${originalPlacement.height} → ${
          scaledPlacement.width
        }x${scaledPlacement.height}`
      );
    });

    console.log(
      `✅ Canvas自适应缩放完成，共处理 ${this.editorElements.length} 个元素`
    );

    // 输出元素顺序信息用于调试
    if (process.env.NODE_ENV === "development") {
      console.log(
        "📋 当前元素顺序:",
        this.editorElements.map((el, index) => ({
          index: index + 1,
          id: el.id,
          type: el.type,
          trackId: el.trackId || "no-track",
        }))
      );
    }
  }

  /**
   * 计算自适应缩放后的placement
   * 使用基于主要维度的智能缩放策略，保持元素在画布中的相对位置
   * @param placement 原始placement
   * @param scaleX X轴缩放比例
   * @param scaleY Y轴缩放比例
   * @param oldCanvasWidth 旧canvas宽度
   * @param oldCanvasHeight 旧canvas高度
   * @param newCanvasWidth 新canvas宽度
   * @param newCanvasHeight 新canvas高度
   * @returns 缩放后的placement
   */
  private calculateAdaptiveScaling(
    placement: Placement,
    scaleX: number,
    scaleY: number,
    oldCanvasWidth: number,
    oldCanvasHeight: number,
    newCanvasWidth: number,
    newCanvasHeight: number
  ): Placement {
    // 根据元素的主要维度决定缩放策略
    const elementWidth = placement.width;
    const elementHeight = placement.height;
    const isWidthDominant = elementWidth >= elementHeight;

    let newWidth: number;
    let newHeight: number;
    let primaryScale: number;

    // 基于主要维度的智能缩放
    if (isWidthDominant) {
      // 宽度主导：主要基于宽度缩放比例，但保持纵横比
      primaryScale = scaleX;
      newWidth = elementWidth * primaryScale;
      newHeight = elementHeight * primaryScale;
    } else {
      // 高度主导：主要基于高度缩放比例，但保持纵横比
      primaryScale = scaleY;
      newWidth = elementWidth * primaryScale;
      newHeight = elementHeight * primaryScale;
    }

    // 计算元素中心点在原canvas中的相对位置
    const elementCenterX = placement.x + elementWidth / 2;
    const elementCenterY = placement.y + elementHeight / 2;
    const relativeX = elementCenterX / oldCanvasWidth;
    const relativeY = elementCenterY / oldCanvasHeight;

    // 在新canvas中保持相同的相对位置
    const newCenterX = relativeX * newCanvasWidth;
    const newCenterY = relativeY * newCanvasHeight;

    // 计算新的左上角位置
    const newX = newCenterX - newWidth / 2;
    const newY = newCenterY - newHeight / 2;

    // 调试信息（仅在开发环境显示）
    if (process.env.NODE_ENV === "development") {
      console.log(
        `  📐 元素尺寸分析: ${elementWidth}x${elementHeight}, 主要维度: ${
          isWidthDominant ? "宽度" : "高度"
        }, 使用缩放比例: ${primaryScale.toFixed(3)}`
      );
      console.log(
        `  📍 相对位置保持: 原中心(${elementCenterX.toFixed(
          1
        )}, ${elementCenterY.toFixed(1)}) 相对位置(${(relativeX * 100).toFixed(
          1
        )}%, ${(relativeY * 100).toFixed(1)}%) → 新中心(${newCenterX.toFixed(
          1
        )}, ${newCenterY.toFixed(1)})`
      );
    }

    return {
      ...placement,
      x: Number(newX.toFixed(2)),
      y: Number(newY.toFixed(2)),
      width: Number(newWidth.toFixed(2)),
      height: Number(newHeight.toFixed(2)),
      // 保持其他属性不变
      scaleX: placement.scaleX || 1,
      scaleY: placement.scaleY || 1,
    };
  }

  /**
   * 计算文本字体大小的缩放比例
   * @param placement 元素placement
   * @param scaleX X轴缩放比例
   * @param scaleY Y轴缩放比例
   * @returns 字体缩放比例
   */
  private calculateFontScale(
    placement: Placement,
    scaleX: number,
    scaleY: number
  ): number {
    // 对于文本元素，使用主要维度的缩放比例
    const elementWidth = placement.width;
    const elementHeight = placement.height;
    const isWidthDominant = elementWidth >= elementHeight;

    // 返回主要维度的缩放比例
    return isWidthDominant ? scaleX : scaleY;
  }

  get currentTimeInMs() {
    return this.timelineManager.currentTimeInMs;
  }

  setCurrentTimeInMs(time: number) {
    this.timelineManager.setCurrentTimeInMs(time);
  }

  setSelectedMenuOption(selectedMenuOption: MenuOption) {
    this.selectedMenuOption = selectedMenuOption;
  }

  setCanvas(canvas: any) {
    this.canvas = canvas;
    if (canvas) {
      canvas.backgroundColor = this.backgroundColor;
      this.elementManager.setCanvas(canvas);

      // 确保画布尺寸与Store状态同步
      const canvasWidth = canvas.getWidth();
      const canvasHeight = canvas.getHeight();
      const ratio = canvasWidth / canvasHeight;

      console.log(
        `🎨 画布初始化完成: ${canvasWidth}x${canvasHeight}, 比例: ${ratio.toFixed(
          3
        )} (${ratio.toFixed(3) === "1.778" ? "16:9 ✓" : "⚠️ 非16:9"})`
      );

      // 如果画布尺寸与Store不同步，更新Store
      if (
        this.canvasWidth !== canvasWidth ||
        this.canvasHeight !== canvasHeight
      ) {
        console.log(
          `🔄 同步画布尺寸: Store(${this.canvasWidth}x${this.canvasHeight}) -> Canvas(${canvasWidth}x${canvasHeight})`
        );
        this.canvasWidth = canvasWidth;
        this.canvasHeight = canvasHeight;
      }
    }
  }

  setBackgroundColor(backgroundColor: string) {
    this.backgroundColor = backgroundColor;
    if (this.canvas) {
      if (backgroundColor.startsWith("linear-gradient")) {
        const gradientObj = new fabric.Gradient({
          type: "linear",
          coords: { x1: 0, y1: 0, x2: this.canvas.width, y2: 0 },
          colorStops: [
            { offset: 0, color: backgroundColor.match(/#[a-f\d]{6}/gi)[0] },
            { offset: 1, color: backgroundColor.match(/#[a-f\d]{6}/gi)[1] },
          ],
        });

        this.canvas.setBackgroundColor(
          gradientObj,
          this.canvas.renderAll.bind(this.canvas)
        );
      } else {
        this.canvas.backgroundColor = backgroundColor;
        this.canvas.renderAll();
      }
    }
    this.saveChange("adjust_canvas");
  }

  async updateEffect(id: string, effect: Effect) {
    console.log(effect);
    const index = this.editorElements.findIndex((element) => element.id === id);
    const element = this.editorElements[index];
    if (
      isEditorVideoElement(element) ||
      isEditorImageElement(element) ||
      isEditorGifElement(element)
    ) {
      element.properties.effect = effect;
    }
    await this.refreshElements();
    this.saveChange("modify_element");
  }

  setVideos(videos: string[]) {
    this.videos = videos;
  }

  addVideoResource(video: string) {
    this.videos = [...this.videos, video];
  }
  addAudioResource(audio: string) {
    this.audios = [...this.audios, audio];
  }
  addImageResource(image: string) {
    this.images = [...this.images, image];
  }

  addAnimation(animation: any) {
    this.animations = [...this.animations, animation];
    this.animationManager.refreshAnimations();
    // 添加动画后验证并清理无效动画
    this.validateAndCleanupAnimations();
    // 保存动画状态更改
    this.saveChange("modify_element");
  }
  updateAnimation(id: string, animation: Animation) {
    const index = this.animations.findIndex((a) => a.id === id);
    if (index !== -1) {
      this.animations[index] = animation;
      this.animationManager?.refreshAnimations();
      // 保存动画状态更改
      this.saveChange("modify_element");
    } else {
      console.warn(`Animation with id ${id} not found`);
    }
  }

  getAnimation(id: string): Animation | undefined {
    return this.animations.find((animation) => animation.id === id);
  }
  /**
   * 刷新动画。
   * 该方法会清除当前的动画时间线，并创建一个新的动画时间线。
   * 遍历所有的动画对象，根据动画类型添加相应的动画效果到时间线中。
   * 最后，启动动画时间线。
   */
  refreshAnimations() {
    this.animationManager.refreshAnimations();
  }

  /**
   * 确保所有元素具有正确的可选择性
   * 在非播放状态下恢复元素的可选择性
   */
  ensureElementSelectability() {
    // 如果正在播放，不恢复可选择性
    if (this.playing) {
      return;
    }

    // 如果是手工具模式，也不恢复可选择性
    if (this.canvasManager.editMode === "hand") {
      return;
    }

    if (!this.canvas) return;

    console.log("确保元素具有正确的可选择性");

    // 遍历所有canvas对象，确保其可选择性正确
    this.canvas.getObjects().forEach((obj) => {
      // 找到对应的编辑器元素
      const element = this.editorElements.find((el) => el.fabricObject === obj);

      if (element) {
        // 根据元素的锁定状态设置可选择性
        obj.selectable = !element.locked;
        obj.evented = !element.locked;
      } else {
        // 如果找不到对应的元素（比如字幕对象），默认设为可选择
        obj.selectable = true;
        obj.evented = true;
      }
    });

    // 重新渲染canvas
    this.canvas.requestRenderAll();
  }

  /**
   * 从动画列表中移除指定的动画。
   * @param id 要移除的动画的唯一标识符。
   * @returns 无返回值。
   */
  removeAnimation(id: string) {
    if (!id) return;
    const animation = this.animations.find((animation) => animation.id === id);
    const elementId = animation?.targetId;
    const element = this.editorElements.find(
      (element) => element.id === elementId
    );
    if (animation) {
      this.animations = this.animations.filter(
        (animation) => animation.id !== id
      );
      this.animationManager.refreshAnimations();
    }
    this.updateEditorElement(element);
    this.canvas.renderAll();
    // 删除动画后验证并清理无效动画
    this.validateAndCleanupAnimations();
    // 保存动画状态更改
    this.saveChange("delete_element");

    // 立即保存到localStorage，确保动画删除操作持久化
    this.saveToLocalStorage();
  }

  /**
   * 设置元素。
   * @param selectedElement 要设置为选中的元素，可以是 EditorElement 对象或 null。
   * @returns 无返回值。
   */
  setSelectedElement(selectedElement: EditorElement | null) {
    // 允许时间线元素和字幕同时选中，不再清除字幕选择
    this.selectedElement = selectedElement;
    if (this.canvas) {
      if (selectedElement?.fabricObject) {
        this.canvas.setActiveObject(selectedElement.fabricObject);
      } else {
        this.canvas.discardActiveObject();
      }
      // 强制重新渲染画布以确保选中状态正确显示
      this.canvas.requestRenderAll();
    }
  }

  updateSelectedElement() {
    if (!this.selectedElement) {
      return;
    }
    const updatedElement = this.editorElements.find(
      (element) => element.id === this.selectedElement!.id
    );
    this.selectedElement = updatedElement || null;
  }

  /**
   * 设置多选元素数组
   * @param selectedElements 要设置为选中的元素数组
   */
  setSelectedElements(selectedElements: EditorElement[]) {
    this.selectedElements = selectedElements;

    // 如果有多选元素，清除单选状态，但保留字幕选择
    if (selectedElements.length > 0) {
      this.selectedElement = null;
    }

    // 更新canvas选中状态
    if (this.canvas) {
      this.canvas.discardActiveObject();
      this.canvas.requestRenderAll();
    }
  }

  /**
   * 添加元素到多选数组
   * @param element 要添加的元素
   */
  addToSelectedElements(element: EditorElement) {
    if (!this.selectedElements.find((el) => el.id === element.id)) {
      this.selectedElements = [...this.selectedElements, element];
      this.selectedElement = null;
      this.captionManager.deselectAllCaptions();
    }
  }

  /**
   * 从多选数组中移除元素
   * @param elementId 要移除的元素ID
   */
  removeFromSelectedElements(elementId: string) {
    this.selectedElements = this.selectedElements.filter(
      (el) => el.id !== elementId
    );
  }

  /**
   * 清除所有选中状态（单选和多选）
   */
  clearAllSelections() {
    this.selectedElement = null;
    this.selectedElements = [];

    // 清除字幕选择并强制更新
    this.captionManager.deselectAllCaptions();
    // 创建新的数组引用以确保MobX检测到变化
    this.captions = [...this.captionManager.captions];

    if (this.canvas) {
      this.canvas.discardActiveObject();
      this.canvas.requestRenderAll();
    }
  }

  /**
   * 全选所有时间线元素（包括字幕）
   */
  selectAllTimelineElements() {
    // 在播放状态下不允许全选
    if (this.playing) {
      return;
    }

    // 获取所有时间线元素和字幕
    const allElements = [...this.editorElements];
    const allCaptions = [...this.captions];

    let selectedCount = 0;

    // 选择所有时间线元素
    if (allElements.length > 0) {
      this.setSelectedElements(allElements);
      selectedCount += allElements.length;
      console.log(`已选中 ${allElements.length} 个时间线元素`);
    } else {
      // 如果没有时间线元素，清除时间线元素选择
      this.selectedElement = null;
      this.selectedElements = [];
    }

    // 选择所有字幕
    if (allCaptions.length > 0) {
      this.captionManager.selectAllCaptions();
      this.captions = this.captionManager.captions;
      selectedCount += allCaptions.length;
      console.log(`已选中 ${allCaptions.length} 个字幕`);
    } else {
      // 如果没有字幕，清除字幕选择
      this.captionManager.deselectAllCaptions();
      this.captions = this.captionManager.captions;
    }

    if (selectedCount > 0) {
      console.log(
        `全选完成：共选中 ${selectedCount} 个元素（${allElements.length} 个时间线元素 + ${allCaptions.length} 个字幕）`
      );
    } else {
      console.log("没有可选择的元素");
    }
  }

  /**
   * 全选所有字幕
   */
  selectAllCaptions() {
    // 在播放状态下不允许全选
    if (this.playing) {
      return;
    }

    // 清除时间线元素选择
    this.selectedElement = null;
    this.selectedElements = [];

    // 选择所有字幕
    this.captionManager.selectAllCaptions();
    this.captions = this.captionManager.captions;

    // 清除Canvas选择
    if (this.canvas) {
      this.canvas.discardActiveObject();
      this.canvas.requestRenderAll();
    }
  }

  /**
   * 检查元素是否被选中（单选或多选）
   * @param elementId 元素ID
   * @returns 是否被选中
   */
  isElementSelected(elementId: string): boolean {
    // 检查单选状态
    if (this.selectedElement?.id === elementId) {
      return true;
    }

    // 检查多选状态
    return this.selectedElements.some((el) => el.id === elementId);
  }

  /**
   * 获取所有选中的元素（单选和多选合并）
   * @returns 所有选中的元素数组
   */
  getAllSelectedElements(): EditorElement[] {
    if (this.selectedElements.length > 0) {
      return this.selectedElements;
    }

    if (this.selectedElement) {
      return [this.selectedElement];
    }

    return [];
  }

  /**
   * 设置编辑元素数组。
   * @param editorElements 要设置的编辑元素数组。
   * @returns 无返回值。
   */
  setEditorElements(editorElements: EditorElement[]) {
    this.elementManager.setEditorElements(editorElements);
  }
  /**
   * 更新编辑元素。
   * @param editorElement 要更新的编辑元素。
   * @param actionType 操作类型，默认为"元素修改"
   * @returns 无返回值。
   */
  updateEditorElement(
    editorElement: EditorElement,
    actionType: HistoryActionType = "modify_element"
  ) {
    this.elementManager.updateEditorElement(editorElement, actionType);
  }

  // 获取拖拽状态
  get isDraggingTimeFrame(): boolean {
    return this._isDraggingTimeFrame;
  }

  // 设置拖拽状态 - 开始拖拽
  setDraggingTimeFrameStart() {
    this._isDraggingTimeFrame = true;
  }

  // 设置拖拽状态 - 结束拖拽
  async setDraggingTimeFrameEnd() {
    this._isDraggingTimeFrame = false;
    // 在拖拽结束时触发一次完整的元素刷新
    await this.refreshElements();
  }

  /**
   * 更新编辑器元素的时间帧
   * 优化版本：在拖拽过程中只更新状态，不触发重渲染
   * @param editorElement 要更新的元素
   * @param timeFrame 新的时间帧
   * @param isDragEnd 是否是拖拽结束的更新
   */
  updateEditorElementTimeFrame(
    editorElement: EditorElement,
    timeFrame: Partial<TimeFrame>,
    isDragEnd: boolean = false
  ) {
    this.elementManager.updateEditorElementTimeFrame(
      editorElement,
      timeFrame,
      isDragEnd
    );
  }

  /**
   * 添加编辑器元素
   * @param editorElement 要添加的元素
   */
  addEditorElement(editorElement: EditorElement) {
    this.elementManager.addEditorElement(editorElement);
  }

  /**
   * 移除编辑器元素
   * @param id 要移除的元素ID
   */
  removeEditorElement(id: string) {
    this.elementManager.removeEditorElement(id);
  }

  setMaxTime(maxTime: number) {
    this.timelineManager.setMaxTime(maxTime);
  }

  updateMaxTime() {
    this.timelineManager.updateMaxTime();
  }

  setPlaying(playing: boolean) {
    this.timelineManager.setPlaying(playing);
  }

  /**
   * 检查是否有可播放的内容（元素或字幕）
   * @returns 如果有可播放的内容返回true，否则返回false
   */
  hasPlayableContent(): boolean {
    const hasElements = this.editorElements.length > 0;
    const hasCaptions =
      this.captionManager && this.captionManager.captions.length > 0;
    return hasElements || hasCaptions;
  }

  /**
   * 更新时间线到指定时间点
   *
   * @param newTime 新的时间点（毫秒）
   */
  updateTimeTo(newTime: number) {
    this.timelineManager.updateTimeTo(newTime);
  }

  /**
   * 处理时间线跳转
   * 该方法负责在用户在时间线上点击或拖动指示器时更新视图
   *
   * @param seek 跳转的目标时间（毫秒）
   */
  handleSeek(seek: number) {
    this.timelineManager.handleSeek(seek);
  }

  /**
   * 重置时间轴指示器位置到0
   * 用于页面刷新或项目加载后确保indicator从头开始
   */
  resetTimelineIndicator() {
    this.timelineManager.resetTimelineIndicator();
  }

  addVideoElement(
    videoElement: HTMLVideoElement,
    videoElementId: string,
    videoMetadata?: any
  ) {
    this.elementManager.addVideoElement(
      videoElement,
      videoElementId,
      videoMetadata
    );
    // 立即渲染canvas
    this.canvas?.renderAll();

    // 延迟再次渲染以确保视频内容显示
    setTimeout(() => {
      this.canvas?.renderAll();
    }, 100);

    // 强制更新时间线以触发视频渲染
    setTimeout(() => {
      this.updateTimeTo(this.currentTimeInMs);
    }, 150);
  }

  addVideo(index: string) {
    this.elementManager.addVideo(index);
  }

  addImageElement(
    imageElement: HTMLImageElement,
    elementId: string,
    imageMetadata?: any
  ) {
    this.elementManager.addImageElement(imageElement, elementId, imageMetadata);
  }

  addImage(id: string) {
    this.elementManager.addImage(id);
  }

  // 添加占位符图片元素
  addImagePlaceholder(
    elementId: string,
    originalSrc: string,
    timeFrame: any,
    imageMetadata?: any
  ) {
    return this.elementManager.addImagePlaceholder(
      elementId,
      originalSrc,
      timeFrame,
      imageMetadata
    );
  }

  // 将占位符替换为实际图片元素
  replacePlaceholderWithImage(
    placeholderElement: any,
    imageElement: HTMLImageElement
  ) {
    this.elementManager.replacePlaceholderWithImage(
      placeholderElement,
      imageElement
    );
  }

  // 添加占位符GIF元素
  addGifPlaceholder(
    elementId: string,
    originalSrc: string,
    timeFrame: any,
    gifMetadata?: any
  ) {
    return this.elementManager.addGifPlaceholder(
      elementId,
      originalSrc,
      timeFrame,
      gifMetadata
    );
  }

  // 将占位符替换为实际GIF元素
  replacePlaceholderWithGif(
    placeholderElement: any,
    gifElement: HTMLImageElement
  ) {
    this.elementManager.replacePlaceholderWithGif(
      placeholderElement,
      gifElement
    );
  }

  // 添加占位符视频元素
  addVideoPlaceholder(
    elementId: string,
    originalSrc: string,
    timeFrame: any,
    videoMetadata?: any
  ) {
    return this.elementManager.addVideoPlaceholder(
      elementId,
      originalSrc,
      timeFrame,
      videoMetadata
    );
  }

  // 将占位符替换为实际视频元素
  replacePlaceholderWithVideo(
    placeholderElement: any,
    videoElement: HTMLVideoElement
  ) {
    this.elementManager.replacePlaceholderWithVideo(
      placeholderElement,
      videoElement
    );
  }

  // 添加占位符音频元素
  addAudioPlaceholder(
    elementId: string,
    originalSrc: string,
    timeFrame: any,
    audioMetadata?: any
  ) {
    return this.elementManager.addAudioPlaceholder(
      elementId,
      originalSrc,
      timeFrame,
      audioMetadata
    );
  }

  // 将占位符替换为实际音频元素
  replacePlaceholderWithAudio(
    placeholderElement: any,
    audioElement: HTMLAudioElement
  ) {
    this.elementManager.replacePlaceholderWithAudio(
      placeholderElement,
      audioElement
    );
  }

  addGifElement(
    gifElement: HTMLImageElement,
    elementId: string,
    gifMetadata?: any
  ) {
    this.elementManager.addGifElement(gifElement, elementId, gifMetadata);
  }

  addGif(id: string) {
    this.elementManager.addGif(id);
  }

  addAudioElement(
    audioElement: HTMLAudioElement,
    audioElementId: string,
    audioMetadata?: any
  ) {
    this.elementManager.addAudioElement(
      audioElement,
      audioElementId,
      audioMetadata
    );
  }

  addAudio(index: string) {
    this.elementManager.addAudio(index);
  }

  addText(options: {
    text: string;
    fontSize: number;
    fontWeight: number;
    id?: string;
    fontFamily?: string;
    fontColor?: string;
    textAlign?: "left" | "center" | "right";
    lineHeight?: number;
    charSpacing?: number;
    styles?: string[];
    strokeWidth?: number;
    strokeColor?: string;
    shadowBlur?: number;
    shadowOffsetX?: number;
    shadowOffsetY?: number;
    shadowColor?: string;
    useGradient?: boolean;
    gradientColors?: string[];
    backgroundColor?: string;
  }) {
    this.elementManager.addText(options);
  }

  /**
   * 在指定时间添加文本元素
   * @param textData 文本数据
   * @param timeFrame 时间帧
   */
  addTextAtTime(textData: any, timeFrame: any) {
    return this.elementManager.addTextAtTime(textData, timeFrame);
  }

  /**
   * 添加图形元素
   * @param shapeType 图形类型
   * @param options 可选配置
   */
  addShapeElement(
    shapeType: ShapeType,
    options?: {
      fill?: string;
      stroke?: string;
      strokeWidth?: number;
      borderRadius?: number;
      id?: string;
    }
  ) {
    this.elementManager.addShapeElement(shapeType, options);
  }

  /**
   * 在指定时间添加形状元素
   * @param shapeType 图形类型
   * @param timeFrame 时间帧
   */
  addShapeElementAtTime(shapeType: ShapeType, timeFrame: any) {
    return this.elementManager.addShapeElementAtTime(shapeType, timeFrame);
  }

  /**
   * 更新所有媒体元素的状态
   * 该方法确保所有媒体元素（视频和音频）的播放状态和当前时间与时间线同步
   */
  updateMediaElements() {
    this.timelineManager.updateMediaElements();
  }

  /**
   * 轻量版的媒体元素更新，只更新当前拖拽的元素
   * 用于拖拽过程中提高性能
   * @param currentElementId 当前正在拖拽的元素ID
   */
  updateMediaElementsLite(currentElementId: string) {
    this.timelineManager.updateMediaElementsLite(currentElementId);
  }

  setVideoFormat(format: string) {
    this.selectedVideoFormat = format;
  }

  saveCanvasToVideoWithAudio() {
    const canvas = document.getElementById("myCanvas") as HTMLCanvasElement;
    const videoSaver = new VideoSaver(
      canvas,
      this.editorElements,
      this.timelineManager.maxTime,
      this.selectedVideoFormat
    );
    videoSaver.saveCanvasToVideoWithAudio();
  }

  async refreshElements() {
    await this.elementManager.refreshElements();
  }

  refreshElement(element: EditorElement) {
    this.elementManager.refreshElement(element);
  }

  alignElement(id: string, alignType: string) {
    this.elementManager.alignElement(id, alignType);
    this.saveChange();
  }

  toggleLockElement(id: string) {
    this.elementManager.toggleLockElement(id);
    this.saveChange("modify_element");
  }

  cloneElement(id: string) {
    this.elementManager.cloneElement(id);
    this.saveChange(); // Add this line
  }

  /**
   * 在指定时间点分割时间线元素
   * @param id 要分割的元素ID
   * @param splitTime 分割时间点（毫秒）
   */
  splitElement(id: string, splitTime: number) {
    this.elementManager.splitElement(id, splitTime);
  }

  deleteElement(id: string) {
    // 添加调试日志
    console.log(`Store.deleteElement called for id: ${id}`);

    // 先从轨道中移除元素
    this.trackManager.handleElementDeleted(id);

    // 然后删除元素（ElementManager.deleteElement方法中已经处理了历史记录保存）
    this.elementManager.deleteElement(id);
  }

  /**
   * 清理空轨道（现在默认包括默认轨道）
   * 用于调试和手动清理
   */
  cleanupEmptyTracks() {
    const hasRemovedTracks = this.trackManager.removeEmptyTracks();
    return hasRemovedTracks;
  }

  /**
   * 保护性清理空轨道（保留默认轨道）
   * 用于需要保留默认轨道的场景
   */
  cleanupEmptyTracksPreserveDefaults() {
    const hasRemovedTracks = this.trackManager.removeEmptyTracks(true);
    return hasRemovedTracks;
  }

  setElementFullscreen(id: string) {
    this.elementManager.setElementFullscreen(id);
    this.saveChange();
  }

  updateElementOpacity(id: string, opacity: number) {
    this.elementManager.updateElementOpacity(id, opacity);
    this.saveChange();
  }

  startCropMode(id: string) {
    this.elementManager.startCropMode(id);
  }

  applyCrop() {
    this.elementManager.applyCrop();
  }

  cancelCrop() {
    this.elementManager.cancelCrop();
  }

  updateTextStyle(
    elementId: string,
    style: Partial<TextEditorElement["properties"]>
  ) {
    this.elementManager.updateTextStyle(elementId, style);
    this.saveChange();
  }

  reorderElements(
    startIndex: number,
    endIndex: number,
    placement?: "above" | "below"
  ) {
    return this.elementManager.reorderElements(startIndex, endIndex, placement);
  }

  updateCanvasOrder() {
    this.elementManager.updateCanvasOrder();
  }

  /**
   * 根据轨道顺序更新Canvas上的元素显示顺序
   * 确保元素在Canvas上的显示顺序与时间线轨道的顺序一致
   * 轨道从上到下，同一轨道内的元素从左到右排序
   */
  updateCanvasOrderByTrackOrder() {
    // 获取按轨道顺序排序的所有元素
    const orderedElements = this.trackManager.getAllElementsInDisplayOrder();

    // 如果没有元素，直接返回
    if (orderedElements.length === 0) return;

    // 调试信息：显示重新排序前的状态
    if (process.env.NODE_ENV === "development") {
      console.log("🔄 开始按轨道顺序重新排列元素...");
      console.log(
        "📋 重排前元素顺序:",
        this.editorElements.map((el, index) => ({
          index: index + 1,
          id: el.id,
          type: el.type,
          trackId: el.trackId || "no-track",
        }))
      );
    }

    // 创建一个新的editorElements数组，保持原有的元素但按照新的顺序排列
    const newEditorElements: EditorElement[] = [];

    // 首先添加所有按轨道顺序排序的元素
    orderedElements.forEach((element) => {
      newEditorElements.push(element);
    });

    // 然后添加所有不在轨道中的元素（如果有的话）
    this.editorElements.forEach((element) => {
      // 检查元素是否已经在新数组中
      const isAlreadyIncluded = newEditorElements.some(
        (el) => el.id === element.id
      );
      if (!isAlreadyIncluded) {
        newEditorElements.push(element);
      }
    });

    // 更新editorElements数组
    this.editorElements = newEditorElements;

    // 调试信息：显示重新排序后的状态
    if (process.env.NODE_ENV === "development") {
      console.log(
        "📋 重排后元素顺序:",
        this.editorElements.map((el, index) => ({
          index: index + 1,
          id: el.id,
          type: el.type,
          trackId: el.trackId || "no-track",
        }))
      );
      console.log("✅ 元素顺序按轨道重新排列完成");
    }

    // 更新Canvas上的元素顺序
    this.elementManager.updateCanvasOrder();
  }

  moveElement(element: any, direction: "up" | "down" | "top" | "bottom") {
    this.elementManager.moveElement(element, direction);
  }

  // 根据拖拽垂直距离交换元素位置
  swapElementsByDrag(elementId: string, deltaY: number) {
    return this.elementManager.swapElementsByDrag(elementId, deltaY);
  }

  flipElement(id: string, flipType: "horizontal" | "vertical") {
    this.elementManager.flipElement(id, flipType);
  }

  setMediaFilter(
    id: string,
    filterType: "brightness" | "contrast" | "saturation" | "hue" | "blur",
    value: number
  ) {
    this.elementManager.setMediaFilter(id, filterType, value);
    this.saveChange();
  }

  setMediaElementBorder(
    id: string,
    property: keyof BorderStyle,
    value: string | number
  ) {
    this.elementManager.setMediaElementBorder(id, property, value);
  }

  /**
   * Gets the EditorElement corresponding to the currently active object on the canvas
   * @returns The EditorElement if found, null otherwise
   */
  getActiveElement(): EditorElement | null {
    return this.elementManager.getActiveElement();
  }

  isActiveElement() {
    return this.elementManager.isActiveElement();
  }

  destroy() {
    // Cancel any ongoing animation frame
    if (this.animationFrameId) {
      cancelAnimationFrame(this.animationFrameId);
      this.animationFrameId = undefined;
    }

    // 清理所有音频连接，防止残留声音
    audioManager.cleanupAllConnections();

    // Stop and remove all media elements
    this.editorElements.forEach((element) => {
      if (element.type === "video" || element.type === "audio") {
        const media = document.getElementById(
          element.properties.elementId
        ) as HTMLMediaElement;
        if (media) {
          media.pause();
          if (element.type === "audio") {
            // 对音频元素进行额外清理
            media.src = "";
            media.load();
          }
          media.remove();
        }
      }
    });

    // Destroy managers
    if (this.timelineManager) {
      this.timelineManager.destroy();
    }
    if (this.animationManager) {
      this.animationManager.destroy();
    }
    if (this.elementManager) {
      this.elementManager.destroy();
    }
    // Clear arrays and reset properties
    this.videos = [];
    this.images = [];
    this.audios = [];
    this.editorElements = [];
    this.animations = [];
    this.selectedElement = null;
    this.selectedElements = [];
    // Clear the canvas
    if (this.canvas) {
      this.canvas.clear();
      this.canvas.dispose();
      this.canvas = null;
    }
  }

  /**
   * 保存更改，使用防抖函数延迟保存
   * @param actionType 操作类型，默认为"其他操作"
   */
  saveChange(actionType?: HistoryActionType) {
    // 如果正在导入状态或正在应用历史记录状态，不保存历史记录
    if (
      this.projectManager.isImporting ||
      this.historyManager.isApplyingHistoryState
    )
      return;
    this.debouncedSaveChange(actionType);
  }

  /**
   * 保存当前状态到历史记录
   * @param actionType 操作类型，默认为"其他操作"
   */
  saveToHistory(actionType?: HistoryActionType) {
    // 如果正在导入状态或正在应用历史记录状态，不保存历史记录
    if (
      this.projectManager.isImporting ||
      this.historyManager.isApplyingHistoryState
    )
      return;
    this.historyManager.saveToHistory(actionType);
  }

  /**
   * 开始操作分组，将连续的操作视为一个操作
   * @param actionType 操作类型
   */
  startHistoryGroup(actionType: HistoryActionType) {
    this.historyManager.startGrouping(actionType);
  }

  /**
   * 结束操作分组
   */
  endHistoryGroup() {
    this.historyManager.endGrouping();
  }

  /**
   * 撤销操作
   * @returns 是否成功撤销
   */
  undo(): boolean {
    return this.historyManager.undo();
  }

  /**
   * 重做操作
   * @returns 是否成功重做
   */
  redo(): boolean {
    return this.historyManager.redo();
  }

  /**
   * 获取可以撤销的操作类型
   * @returns 可以撤销的操作类型，如果没有则返回null
   */
  getUndoActionType(): HistoryActionType | null {
    return this.historyManager.getUndoActionType();
  }

  /**
   * 获取可以重做的操作类型
   * @returns 可以重做的操作类型，如果没有则返回null
   */
  getRedoActionType(): HistoryActionType | null {
    return this.historyManager.getRedoActionType();
  }

  // Timeline panning methods
  startTimelinePan(startX: number) {
    this.timelineManager.startTimelinePan(startX);
  }

  updateTimelinePan(currentX: number) {
    this.timelineManager.updateTimelinePan(currentX);
  }

  endTimelinePan() {
    this.timelineManager.endTimelinePan();
  }

  resetTimelinePan() {
    this.timelineManager.resetTimelinePan();
  }

  // Handle wheel scrolling for timeline with improved performance
  handleTimelineWheel(deltaX: number) {
    this.timelineManager.handleTimelineWheel(deltaX);
  }

  // Set timeline pan offset directly (for scrollbar)
  setTimelinePanOffset(offsetX: number) {
    this.timelineManager.setTimelinePanOffset(offsetX);
  }

  exportCanvasState() {
    return this.projectManager.exportCanvasState();
  }

  importCanvasState(jsonState: string, needLoading: boolean = true) {
    return this.projectManager.importCanvasState(jsonState, needLoading);
  }

  saveToLocalStorage() {
    // 委托给ProjectManager处理项目专用的存储
    this.projectManager.saveToLocalStorage();
  }

  loadFromLocalStorage() {
    return this.projectManager.loadFromLocalStorage();
  }

  /**
   * 创建一个防抖函数
   * @param func 要执行的函数
   * @param wait 等待时间（毫秒）
   * @returns 防抖函数
   */
  private debounce<T extends (...args: any[]) => any>(func: T, wait: number) {
    let timeout: NodeJS.Timeout;
    return (...args: Parameters<T>) => {
      clearTimeout(timeout);
      timeout = setTimeout(() => func.apply(this, args), wait);
    };
  }

  /**
   * 设置加载状态
   * @param isLoading 是否正在加载
   * @param message 加载消息
   */
  setLoading(isLoading: boolean, message: string = "") {
    this.isLoading = isLoading;
    this.loadingMessage = message;
  }

  // 防抖保存时间线高度到localStorage
  private debouncedSaveTimelineHeight = this.debounce((height: number) => {
    localStorage.setItem("timeline-height", height.toString());
  }, 300);

  /**
   * 设置时间线高度
   * @param height 时间线高度（像素）
   */
  setTimelineHeight(height: number) {
    // 限制高度范围在150-600px之间
    this.timelineHeight = Math.max(150, Math.min(600, height));
    // 防抖保存到localStorage，避免频繁写入
    this.debouncedSaveTimelineHeight(this.timelineHeight);
  }

  /**
   * 从动画列表中移除指定元素组的动画。
   * @param elementId 移除动画的元素ID
   * @param group 要移除的动画组名称
   * @returns 无返回值。
   */
  removeAnimationByGroup(elementId: string, group: string) {
    console.log("removeAnimationByGroup", elementId, group);
    // Find and remove all animations matching both elementId and group
    this.animations = this.animations.filter(
      (animation) =>
        !(animation.targetId === elementId && animation.group === group)
    );
    console.log("this.animations", this.animations);
    // Get the element to update
    const element = this.editorElements.find(
      (element) => element.id === elementId
    );

    // Refresh animations and update the element
    this.animationManager.refreshAnimations();
    if (element) {
      this.updateEditorElement(element);
      this.canvas.renderAll();
    }
    // 删除动画组后验证并清理无效动画
    this.validateAndCleanupAnimations();
    // 保存动画状态更改
    this.saveChange("delete_element");

    // 立即保存到localStorage，确保动画删除操作持久化
    this.saveToLocalStorage();
  }

  /**
   * 验证并清理无效的动画
   * 确保所有动画的targetId都有对应的元素，删除无效的动画
   */
  private validateAndCleanupAnimations() {
    console.log("🔧 验证动画设置界面的动画有效性");

    if (!this.animations || this.animations.length === 0) {
      console.log("📝 没有动画需要验证");
      return;
    }

    // 创建元素ID集合，用于快速查找
    const elementIds = new Set(
      this.editorElements.map((element) => element.id)
    );

    // 记录原始动画数量
    const originalCount = this.animations.length;

    // 过滤出有效的动画
    const validAnimations = this.animations.filter((animation) => {
      if (!animation.targetId) {
        console.log(`⚠️ 发现没有targetId的动画: ${animation.id}`);
        return false;
      }

      if (!elementIds.has(animation.targetId)) {
        console.log(
          `🗑️ 删除无效动画 ${animation.id}，目标元素 ${animation.targetId} 不存在`
        );
        return false;
      }

      return true;
    });

    // 更新动画数组
    this.animations = validAnimations;

    const removedCount = originalCount - validAnimations.length;
    if (removedCount > 0) {
      console.log(
        `✅ 动画验证完成，删除了 ${removedCount} 个无效动画，保留 ${validAnimations.length} 个有效动画`
      );
      // 如果有动画被删除，需要刷新动画管理器
      this.animationManager.refreshAnimations();
      this.canvas.renderAll();
    } else {
      console.log(`✅ 所有 ${validAnimations.length} 个动画都有效，无需清理`);
    }
  }

  async exportVideo(format: string = "mp4", quality: string = "medium") {
    return this.projectManager.exportVideo(format, quality);
  }

  setProjectName(name: string) {
    this.projectManager.setProjectName(name);
  }

  /**
   * 设置编辑模式
   * @param mode 编辑模式：'move'或'hand'
   */
  setEditMode(mode: "move" | "hand") {
    this.canvasManager.setEditMode(mode);
  }

  /**
   * 更新画布缩放值
   * @param scale 新的缩放值
   * @param translation 新的平移值（可选）
   * @param shouldSave 是否应该保存到本地存储，默认为true
   */
  updateCanvasScale(
    scale: number,
    translation?: { x: number; y: number },
    shouldSave: boolean = true
  ) {
    this.canvasManager.updateCanvasScale(scale, translation, shouldSave);
  }

  /**
   * 放大画布
   * @param step 缩放步长，默认为0.1
   */
  zoomIn(step: number = 0.1) {
    return this.canvasManager.zoomIn(step);
  }

  /**
   * 缩小画布
   * @param step 缩放步长，默认为0.1
   */
  zoomOut(step: number = 0.1) {
    return this.canvasManager.zoomOut(step);
  }

  /**
   * 重置画布缩放
   */
  resetZoom() {
    return this.canvasManager.resetZoom();
  }

  // Format time string to "00:00:00" format
  formatTimeString(timeStr: string): string {
    return this.captionManager.formatTimeString(timeStr);
  }

  // Forward caption methods to captionManager
  addCaption() {
    this.captionManager.addCaption();
    this.captions = this.captionManager.captions;
  }

  deleteCaption(id: string) {
    this.captionManager.deleteCaption(id);
    this.captions = this.captionManager.captions;
  }

  selectCaption(id: string) {
    // 选中字幕时，如果是单选模式，取消时间线元素的选中状态
    // 但保留多选状态，允许字幕和时间线元素同时选中
    this.selectedElement = null;

    this.captionManager.selectCaption(id);
    this.captions = this.captionManager.captions;
  }

  /**
   * 切换字幕选中状态（用于多选）
   */
  toggleCaptionSelection(id: string) {
    // 多选模式下不清除时间线元素选择，允许混合选择
    this.captionManager.toggleCaptionSelection(id);
    this.captions = this.captionManager.captions;
  }

  /**
   * 添加字幕到选中列表
   */
  addCaptionToSelection(id: string) {
    // 多选模式下不清除时间线元素选择，允许混合选择
    this.captionManager.addCaptionToSelection(id);
    this.captions = this.captionManager.captions;
  }

  /**
   * 从选中列表中移除字幕
   */
  removeCaptionFromSelection(id: string) {
    this.captionManager.removeCaptionFromSelection(id);
    this.captions = this.captionManager.captions;
  }

  /**
   * 获取所有选中的字幕
   */
  getSelectedCaptions(): Caption[] {
    return this.captionManager.getSelectedCaptions();
  }

  /**
   * 检查字幕是否被选中
   */
  isCaptionSelected(id: string): boolean {
    return this.captionManager.isCaptionSelected(id);
  }

  updateCaption(id: string, field: keyof Caption, value: string) {
    this.captionManager.updateCaption(id, field, value);
    this.captions = this.captionManager.captions;
  }

  updateCaptionTimeFrame(id: string, startTime: string, endTime: string) {
    // 验证时间格式并转换为毫秒进行比较
    const startTimeMs = this.captionManager.timeStringToMilliseconds(startTime);
    const endTimeMs = this.captionManager.timeStringToMilliseconds(endTime);

    // 最终验证：确保startTime < endTime
    if (startTimeMs >= endTimeMs) {
      console.warn(
        `updateCaptionTimeFrame: 检测到startTime >= endTime，字幕ID: ${id}`
      );
      console.warn(
        `startTime: ${startTime} (${startTimeMs}ms), endTime: ${endTime} (${endTimeMs}ms)`
      );
      // 如果时间错乱，不执行更新
      return;
    }

    this.captionManager.updateCaption(id, "startTime", startTime);
    this.captionManager.updateCaption(id, "endTime", endTime);
    this.captionManager.sortCaptionsByStartTime();
    this.captions = this.captionManager.captions;

    // 更新字幕时间后，检查当前时间线指示器位置是否需要更新字幕显示
    this.captionManager.updateCurrentCaption(this.currentTimeInMs);
  }

  formatCaptionTime(timeStr: string): string {
    return this.captionManager.formatCaptionTime(timeStr);
  }

  addCaptionBetween(index: number) {
    this.captionManager.addCaptionBetween(index);
    this.captions = this.captionManager.captions;
  }

  mergeCaptions(index: number) {
    this.captionManager.mergeCaptions(index);
    this.captions = this.captionManager.captions;
  }

  deselectAllCaptions() {
    this.captionManager.deselectAllCaptions();
    this.captions = this.captionManager.captions;
  }

  exportCaptionsAsSRT(): string {
    return this.captionManager.exportCaptionsAsSRT();
  }

  exportAndDownloadCaptionsAsSRT() {
    this.captionManager.exportAndDownloadCaptionsAsSRT();
  }

  updateGlobalCaptionStyle(
    styleProps: Partial<import("../types").CaptionStyle>
  ) {
    this.captionManager.updateGlobalCaptionStyle(styleProps);
  }

  getGlobalCaptionStyle(): import("../types").CaptionStyle {
    return this.captionManager.globalCaptionStyle;
  }

  getSelectedCaption(): Caption | null {
    return this.captionManager.getSelectedCaption();
  }

  importCaptionsFromSRT(srtContent: string) {
    this.captionManager.importCaptionsFromSRT(srtContent);
    this.captions = this.captionManager.captions;
    this.saveChange();
  }

  clearAllCaptions() {
    this.captionManager.clearAllCaptions();
    this.captions = this.captionManager.captions;
    this.saveChange();

    // 立即保存到localStorage，确保清除字幕操作持久化
    this.saveToLocalStorage();
  }

  setTimelineDisplayDuration(duration: number) {
    this.timelineManager.setTimelineDisplayDuration(duration);
  }

  // Fit the timeline to show all content
  fitTimelineToContent() {
    this.timelineManager.fitTimelineToContent();
  }

  get canvasScale() {
    return this.canvasManager.canvasScale;
  }
  get canvasTranslation() {
    return this.canvasManager.canvasTranslation;
  }

  get editMode() {
    return this.canvasManager.editMode;
  }

  get projectName() {
    return this.projectManager.projectName;
  }

  // Timeline-related getters
  get maxTime() {
    return this.timelineManager.maxTime;
  }

  get timelineDisplayDuration() {
    return this.timelineManager.timelineDisplayDuration;
  }

  get maxDuration() {
    return this.timelineManager.maxDuration;
  }

  get currentKeyFrame() {
    return this.timelineManager.currentKeyFrame;
  }

  get fps() {
    return this.timelineManager.fps;
  }

  get playing() {
    return this.timelineManager.playing;
  }

  get animationTimeLine() {
    return this.timelineManager.animationTimeLine;
  }

  get timelinePan() {
    return this.timelineManager.timelinePan;
  }

  // 全局加载状态管理方法
  startGlobalLoading(
    elementId: string,
    message: string,
    progress?: number | null,
    stage?: string
  ) {
    this.loadingElements.add(elementId);
    this.globalLoadingMessage = message;
    if (typeof progress === "number")
      this.globalLoadingProgress = Math.max(0, Math.min(100, progress));
    if (progress === null) this.globalLoadingProgress = null;
    if (stage !== undefined) this.globalLoadingStage = stage || "";
  }

  updateGlobalLoadingProgress(progress?: number | null, stage?: string) {
    if (typeof progress === "number")
      this.globalLoadingProgress = Math.max(0, Math.min(100, progress));
    if (progress === null) this.globalLoadingProgress = null;
    if (stage !== undefined)
      this.globalLoadingStage = stage || this.globalLoadingStage;
  }

  finishGlobalLoading(elementId: string) {
    this.loadingElements.delete(elementId);
    if (this.loadingElements.size === 0) {
      this.globalLoadingMessage = "";
      this.globalLoadingProgress = null;
      this.globalLoadingStage = "";
    }
  }

  get hasGlobalLoading() {
    return this.loadingElements.size > 0;
  }
}
