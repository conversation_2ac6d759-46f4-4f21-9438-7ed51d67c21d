import { fabric } from "fabric";
import { makeAutoObservable } from "mobx";
import { Store } from "./Store";
import { CONSTANTS } from "./constants";

export class CanvasManager {
  private store: Store;

  editMode: "move" | "hand" = "move";
  canvasScale: number = CONSTANTS.CANVAS.DEFAULT_SCALE;
  canvasTranslation: { x: number; y: number } = { x: 0, y: 0 };

  _pan: {
    enable: boolean;
    isDragging: boolean;
    lastPosX: number;
    lastPosY: number;
  };

  constructor(store: Store) {
    this.store = store;
    this._pan = {
      enable: false,
      isDragging: false,
      lastPosX: 0,
      lastPosY: 0,
    };
    makeAutoObservable(this);

    // 初始化时设置更好的默认缩放
    this.initializeOptimalScale();
  }

  /**
   * 初始化最优缩放比例
   * 在新项目创建时调用，设置更适合当前窗口的缩放比例
   */
  private initializeOptimalScale() {
    // 延迟执行，确保DOM已经渲染完成
    setTimeout(() => {
      const { scale, translation } = this.calculateOptimalCanvasScale();
      this.updateCanvasScale(scale, translation, false); // 初始化时不保存到localStorage
    }, 100);
  }

  /**
   * 公开方法：重新计算并应用最优缩放
   * 可以在新项目创建、模板应用后等场景调用
   */
  resetToOptimalScale() {
    const { scale, translation } = this.calculateOptimalCanvasScale();
    this.updateCanvasScale(scale, translation);
    console.log(
      `🎯 重置到最优缩放: scale=${scale.toFixed(2)}, translation=`,
      translation
    );
  }

  get canvas(): fabric.Canvas | null {
    return this.store.canvas;
  }

  /**
   * 设置编辑模式
   * @param mode 编辑模式：'move'或'hand'
   */
  setEditMode(mode: "move" | "hand") {
    // 如果模式没有变化，不做任何操作
    if (this.editMode === mode) return;

    // 保存旧模式，用于日志记录
    const oldMode = this.editMode;
    console.log(`切换编辑模式: ${oldMode} -> ${mode}`);
    this.editMode = mode;

    if (!this.canvas) return;

    // 如果切换到手工具模式，禁用所有对象的可选择性和可移动性
    if (mode === "hand") {
      this.canvas.discardActiveObject();
      this.store.clearAllSelections();

      // 禁用所有对象的可选择性和可移动性
      this.canvas.getObjects().forEach((obj) => {
        obj.selectable = false;
        obj.evented = false; // 禁止所有事件交互
      });

      this.canvas.requestRenderAll();
    } else {
      // 当切换回移动模式时，启用所有对象的可选择性和可移动性
      this.canvas.getObjects().forEach((obj) => {
        obj.selectable = true;
        obj.evented = true; // 恢复事件交互
      });

      this.canvas.requestRenderAll();
    }
  }

  /**
   * 更新画布缩放值
   * @param scale 新的缩放值
   * @param translation 新的平移值（可选）
   * @param shouldSave 是否应该保存到本地存储，默认为true
   */
  updateCanvasScale(
    scale: number,
    translation?: { x: number; y: number },
    shouldSave: boolean = true
  ) {
    // 确保缩放比例正确 - 统一缩放保持16:9比例
    const clampedScale = this._clampScale(scale);
    this.canvasScale = clampedScale;

    if (translation) {
      this.canvasTranslation = translation;
    }

    // 验证并保护画布比例
    this._validateCanvasRatio();

    // 如果需要保存且不是在导入过程中，则直接保存到localStorage（不记录历史）
    if (shouldSave && !this.store.projectManager.isImporting) {
      this.store.projectManager.saveToLocalStorage();
    }
  }

  private _clampScale(scale: number): number {
    return Math.max(
      CONSTANTS.CANVAS.MIN_SCALE,
      Math.min(CONSTANTS.CANVAS.MAX_SCALE, scale)
    );
  }

  /**
   * 验证并保护画布比例，确保始终保持16:9
   */
  private _validateCanvasRatio() {
    if (!this.canvas) return;

    const currentWidth = this.canvas.getWidth();
    const currentHeight = this.canvas.getHeight();
    const currentRatio = currentWidth / currentHeight;
    const expectedRatio =
      CONSTANTS.CANVAS.DEFAULT_WIDTH / CONSTANTS.CANVAS.DEFAULT_HEIGHT; // 16:9 = 1.778

    // 容忍度：允许0.001的误差
    const tolerance = 0.001;

    if (Math.abs(currentRatio - expectedRatio) > tolerance) {
      console.warn(
        `⚠️ 画布比例异常: 当前=${currentRatio.toFixed(
          3
        )}, 期望=${expectedRatio.toFixed(3)}`
      );

      // 自动修正画布尺寸，保持宽度不变，调整高度
      const correctedHeight = Math.round(currentWidth / expectedRatio);

      console.log(
        `🔧 自动修正画布尺寸: ${currentWidth}x${currentHeight} -> ${currentWidth}x${correctedHeight}`
      );

      // 更新canvas尺寸
      this.canvas.setWidth(currentWidth);
      this.canvas.setHeight(correctedHeight);

      // 同步Store状态
      this.store.canvasWidth = currentWidth;
      this.store.canvasHeight = correctedHeight;

      this.canvas.requestRenderAll();
    }
  }

  /**
   * 计算基于当前画布状态的缩放比例，考虑时间线高度
   */
  private calculateOptimalCanvasScale() {
    const windowWidth = window.innerWidth;
    const windowHeight = window.innerHeight;
    const timelineHeight = this.store.timelineHeight;
    const navbarHeight = 60; // 导航栏高度

    // 可用的画布区域高度
    const availableHeight = windowHeight - timelineHeight - navbarHeight;

    // 计算缩放比例，确保画布不被遮挡，但使画布显示更大
    const scaleX = (windowWidth * 0.8) / this.store.canvasWidth; // 增加水平使用率到80%
    const scaleY = (availableHeight * 0.8) / this.store.canvasHeight; // 增加垂直使用率到80%

    // ⚡ 关键：使用较小的缩放值确保等比例缩放，保持16:9比例
    // 这确保画布在任何窗口尺寸下都保持正确的宽高比
    const uniformScale = Math.min(scaleX, scaleY);

    // 使用更大的最小缩放值，并提高最大缩放限制
    const minScale = Math.max(CONSTANTS.CANVAS.DEFAULT_SCALE, 0.25); // 最小不低于0.25
    const scale = Math.max(minScale, Math.min(uniformScale, 1.5)); // 限制最大缩放为1.5

    // 计算画布居中位置
    const x = windowWidth / 2;
    const y = availableHeight / 2 + navbarHeight;

    // 添加调试信息
    const canvasRatio = this.store.canvasWidth / this.store.canvasHeight;
    const expectedRatio =
      CONSTANTS.CANVAS.DEFAULT_WIDTH / CONSTANTS.CANVAS.DEFAULT_HEIGHT;
    const ratioOk = Math.abs(canvasRatio - expectedRatio) < 0.001;

    console.log(`📐 缩放计算详情:
      画布尺寸: ${this.store.canvasWidth}x${
      this.store.canvasHeight
    } (比例: ${canvasRatio.toFixed(3)} ${ratioOk ? "✓" : "⚠️"})
      期望比例: ${expectedRatio.toFixed(3)} (16:9)
      窗口尺寸: ${windowWidth}x${windowHeight}
      可用区域: ${windowWidth}x${availableHeight}
      计算缩放: X=${scaleX.toFixed(3)}, Y=${scaleY.toFixed(
      3
    )}, 统一=${uniformScale.toFixed(3)}
      最终缩放: ${scale.toFixed(3)} (等比例缩放保护 ✓)
      居中位置: (${x}, ${y})`);

    return { scale, translation: { x, y } };
  }

  /**
   * 放大画布（保持16:9比例）
   * @param step 缩放步长，默认为0.1
   */
  zoomIn(step: number = CONSTANTS.CANVAS.ZOOM_STEP) {
    // 获取当前最优缩放状态作为基准
    const { scale: optimalScale, translation: optimalTranslation } =
      this.calculateOptimalCanvasScale();

    // 基于当前缩放值进行放大，但使用最新的平移位置
    const newScale = this.canvasScale + step;
    this.updateCanvasScale(newScale, optimalTranslation);

    console.log(
      `🔍 放大画布: ${(this.canvasScale - step).toFixed(
        3
      )} -> ${this.canvasScale.toFixed(3)} (保持16:9比例)`
    );

    return {
      scale: this.canvasScale,
      translation: this.canvasTranslation,
    };
  }

  /**
   * 缩小画布（保持16:9比例）
   * @param step 缩放步长，默认为0.1
   */
  zoomOut(step: number = CONSTANTS.CANVAS.ZOOM_STEP) {
    // 获取当前最优缩放状态作为基准
    const { scale: optimalScale, translation: optimalTranslation } =
      this.calculateOptimalCanvasScale();

    // 基于当前缩放值进行缩小，但使用最新的平移位置
    const newScale = this.canvasScale - step;
    this.updateCanvasScale(newScale, optimalTranslation);

    console.log(
      `🔍 缩小画布: ${(this.canvasScale + step).toFixed(
        3
      )} -> ${this.canvasScale.toFixed(3)} (保持16:9比例)`
    );

    return {
      scale: this.canvasScale,
      translation: this.canvasTranslation,
    };
  }

  /**
   * 重置画布缩放（保持16:9比例）
   */
  resetZoom() {
    // 使用动态计算的最优缩放状态
    const { scale, translation } = this.calculateOptimalCanvasScale();

    this.updateCanvasScale(scale, translation);

    console.log(`🔄 重置画布缩放: ${scale.toFixed(3)} (16:9比例保护 ✓)`);

    return {
      scale: this.canvasScale,
      translation: this.canvasTranslation,
    };
  }
}
